import { createClient } from '@supabase/supabase-js'
import { config } from './config'

// Create a single supabase client for interacting with your database
export const supabase = createClient(
  config.supabase.url,
  config.supabase.anonKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    }
  }
)

// Types for Supabase Auth
export interface AuthUser {
  id: string
  email?: string
  user_metadata: {
    name?: string
    avatar_url?: string
  }
  app_metadata: {
    role?: 'admin' | 'distributor' | 'user'
  }
}

// Helper function to get user profile data
export async function getUserProfile(userId: string) {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error fetching user profile:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      })
      return null
    }

    return data
  } catch (err) {
    console.error('Unexpected error fetching user profile:', err)
    return null
  }
}

// Helper function to create or update user profile
export async function upsertUserProfile(userId: string, profileData: {
  name: string
  role?: 'admin' | 'distributor' | 'user'
  wallet_balance?: number
  avatar?: string
  phone?: string
  tenant_id?: string
  email?: string
}) {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert({
        id: userId,
        ...profileData,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error upserting user profile:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      })
      return { success: false, error: error.message }
    }

    return { success: true, data }
  } catch (err) {
    console.error('Unexpected error upserting user profile:', err)
    return { success: false, error: 'حدث خطأ غير متوقع' }
  }
}

// Helper function to check if user is admin
export async function isUserAdmin(userId: string): Promise<boolean> {
  const profile = await getUserProfile(userId)
  return profile?.role === 'admin'
}

// Helper function to sign out
export async function signOut() {
  const { error } = await supabase.auth.signOut()
  if (error) {
    console.error('Error signing out:', error)
    return { success: false, error: error.message }
  }
  return { success: true }
}

// Helper function to sign in with email and password (tenant-aware)
export async function signInWithEmail(email: string, password: string, tenantId?: string) {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      console.error('Error signing in:', error)
      return { success: false, error: error.message }
    }

    // Verify the user belongs to the current tenant
    if (data.user && tenantId) {
      const profile = await getUserProfile(data.user.id)
      if (!profile || profile.tenant_id !== tenantId) {
        await supabase.auth.signOut()
        return { success: false, error: 'لا يوجد حساب بهذا البريد الإلكتروني في هذا المتجر' }
      }
    }

    return { success: true, data }
  } catch (err) {
    console.error('Unexpected error during signin:', err)
    return { success: false, error: 'حدث خطأ غير متوقع أثناء تسجيل الدخول' }
  }
}

// Helper function to sign up with email and password (tenant-aware)
export async function signUpWithEmail(email: string, password: string, name: string, tenantId?: string) {
  try {
    // Check if user already exists in this tenant
    if (tenantId) {
      const { data: existingProfile } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('email', email)
        .eq('tenant_id', tenantId)
        .single()

      if (existingProfile) {
        return { success: false, error: 'يوجد حساب بهذا البريد الإلكتروني في هذا المتجر بالفعل' }
      }
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: name,
          tenant_id: tenantId
        }
      }
    })

    if (error) {
      console.error('Error signing up:', error)
      return { success: false, error: error.message }
    }

    // Create tenant-specific user profile
    if (data.user && tenantId) {
      // Wait a moment for the trigger to execute
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Check if profile was created by trigger
      let profile = await getUserProfile(data.user.id)

      if (!profile) {
        // Fallback: create profile manually if trigger failed
        console.log('Trigger did not create profile, creating manually...')
        const result = await upsertUserProfile(data.user.id, {
          name: name,
          role: 'user',
          wallet_balance: 0,
          tenant_id: tenantId,
          email: email
        })

        if (!result.success) {
          console.error('Failed to create user profile:', result.error)
          return { success: false, error: 'فشل في إنشاء ملف المستخدم' }
        }
      } else {
        // Update profile with tenant_id if not set
        if (!profile.tenant_id) {
          await upsertUserProfile(data.user.id, {
            name: profile.name,
            role: profile.role,
            wallet_balance: profile.wallet_balance,
            tenant_id: tenantId,
            email: email
          })
        }
      }
    }

    return { success: true, data }
  } catch (err) {
    console.error('Unexpected error during signup:', err)
    return { success: false, error: 'حدث خطأ غير متوقع أثناء إنشاء الحساب' }
  }
}

// Helper function to reset password
export async function resetPassword(email: string) {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${config.app.url}/auth/reset-password`
  })
  
  if (error) {
    console.error('Error resetting password:', error)
    return { success: false, error: error.message }
  }
  
  return { success: true }
}

// Helper function to update password
export async function updatePassword(newPassword: string) {
  const { error } = await supabase.auth.updateUser({
    password: newPassword
  })
  
  if (error) {
    console.error('Error updating password:', error)
    return { success: false, error: error.message }
  }
  
  return { success: true }
}

export default supabase
