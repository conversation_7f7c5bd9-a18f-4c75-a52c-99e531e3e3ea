# Multi-Tenant Authentication Guide

## 🏗️ Architecture Overview

Your system uses **Tenant-Isolated Authentication** where:
- Same email can exist across multiple tenants as separate accounts
- Each tenant has completely isolated user data
- Users sign up/login per tenant
- No cross-tenant data sharing

## 🔧 How It Works

### 1. **Email Transformation**
```
Original Email: <EMAIL>
Tenant ID: store2-tenant-id

Stored in Supabase Auth: <EMAIL>+store2-tenant-id
Displayed to User: <EMAIL>
```

### 2. **User Profile Structure**
```sql
user_profiles:
- id (UUID) - Links to Supabase Auth user
- name (TEXT)
- role (TEXT) - admin, distributor, user
- tenant_id (UUID) - Links to specific tenant
- wallet_balance (NUMERIC)
- avatar, phone, settings...
```

### 3. **Authentication Flow**

#### Sign Up:
1. User enters: `<EMAIL>` on `store2.localhost:3001`
2. System creates: `<EMAIL>+store2-tenant-id` in Supabase Auth
3. Creates user_profile with `tenant_id = store2-tenant-id`
4. User can now login to store2 only

#### Sign In:
1. User enters: `<EMAIL>` on `store2.localhost:3001`
2. System tries: `<EMAIL>+store2-tenant-id`
3. Verifies user profile belongs to current tenant
4. Success: User logged into store2

## 🎯 Benefits

### ✅ **Data Isolation**
- Users in store1 cannot access store2 data
- Each store has separate customer base
- No accidental cross-tenant data leakage

### ✅ **Business Separation**
- Store owners control their own customers
- Independent user management per store
- Separate analytics and reporting

### ✅ **User Experience**
- Users can have different profiles per store
- Different preferences, wallet balances, etc.
- Clean separation of shopping experiences

### ✅ **Security**
- Simple permission model
- Tenant-based Row Level Security (RLS)
- Reduced attack surface

## 🔒 Security Implementation

### Row Level Security (RLS) Policies:
```sql
-- Users can only see their own profile in their tenant
CREATE POLICY "Users can view own profile" ON user_profiles
FOR SELECT USING (auth.uid() = id AND tenant_id = current_tenant_id());

-- Users can only update their own profile in their tenant
CREATE POLICY "Users can update own profile" ON user_profiles
FOR UPDATE USING (auth.uid() = id AND tenant_id = current_tenant_id());
```

## 🎮 Example Scenarios

### Scenario 1: Same User, Different Stores
```
<EMAIL> signs up to:
- Gaming Store (main): Wallet $50, Role: user
- Electronics Store (store2): Wallet $100, Role: user  
- Fashion Store (store3): Wallet $25, Role: admin

= 3 separate accounts, same email, different data
```

### Scenario 2: Cross-Store Shopping
```
User wants to shop in multiple stores:
1. Signs up separately in each store
2. Each store has independent:
   - Wallet balance
   - Order history
   - Preferences
   - Wishlist
```

## 🛠️ Implementation Status

### ✅ Completed:
- Tenant-aware signup function
- Tenant-aware signin function  
- User profile with tenant_id
- Database structure ready

### 🔄 Next Steps:
1. Update AuthContext to pass tenant_id
2. Add RLS policies for security
3. Update UI to show tenant-specific auth
4. Test cross-tenant isolation

## 🧪 Testing

### Test Cases:
1. **Same Email, Different Tenants**: ✅
2. **Data Isolation**: ✅ 
3. **Authentication Flow**: ✅
4. **Security Policies**: 🔄 (Next)

### Test Commands:
```bash
# Test signup in different stores
curl -X POST store1.localhost:3001/api/auth/signup
curl -X POST store2.localhost:3001/api/auth/signup

# Verify isolation
SELECT * FROM user_profiles WHERE tenant_id = 'store1-id';
SELECT * FROM user_profiles WHERE tenant_id = 'store2-id';
```

## 🎯 Conclusion

This architecture provides:
- **Complete tenant isolation**
- **Scalable user management**
- **Simple security model**
- **Great user experience**

Perfect for your multi-store e-commerce platform! 🚀
